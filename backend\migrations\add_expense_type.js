const pool = require('../database');

async function addExpenseTypeColumn() {
  let client;
  
  try {
    client = await pool.connect();
    
    console.log('Starting migration: Adding expense_type column to expenses table...');
    
    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'expenses' AND column_name = 'expense_type';
    `;
    
    const { rows } = await client.query(checkColumnQuery);
    
    if (rows.length === 0) {
      // Column doesn't exist, so add it
      const addColumnQuery = `
        ALTER TABLE expenses 
        ADD COLUMN expense_type VARCHAR(20) DEFAULT 'variable' 
        CHECK (expense_type IN ('fixed', 'variable'));
      `;
      
      await client.query(addColumnQuery);
      console.log('Migration successful: expense_type column added to expenses table.');
      
      // Update existing recurring expenses to be 'fixed' type
      const updateRecurringQuery = `
        UPDATE expenses 
        SET expense_type = 'fixed' 
        WHERE is_recurring = true;
      `;
      
      const updateResult = await client.query(updateRecurringQuery);
      console.log(`Updated ${updateResult.rowCount} existing recurring expenses to 'fixed' type.`);
      
    } else {
      console.log('Column expense_type already exists in expenses table. No changes made.');
    }
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    if (client) {
      client.release();
    }
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  addExpenseTypeColumn()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

module.exports = addExpenseTypeColumn;
