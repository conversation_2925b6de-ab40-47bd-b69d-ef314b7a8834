<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#ffffff">
    <meta name="format-detection" content="telephone=no">
    <title>Convert to Receipt - Shans System</title>
    <script src="auth-utils.js"></script>
    <style>
        /* Base styling matching main website */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            backface-visibility: hidden;
            transform: translateZ(0);
            will-change: transform;
        }

        input, select, textarea, button {
            -webkit-appearance: none;
            appearance: none;
            user-select: text;
            -webkit-user-select: text;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 800px;
            margin: auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        h1, h2 {
            color: #333;
        }

        .section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }

        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .item-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #f9f9f9;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            height: fit-content;
            display: flex;
            flex-direction: column;
        }

        .item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .item-reference {
            font-weight: bold;
            color: #4a90e2;
            font-size: 16px;
        }

        .item-date {
            color: #666;
            font-size: 14px;
        }

        .item-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            font-size: 14px;
        }

        .detail-label {
            font-weight: bold;
            color: #333;
        }

        .detail-value {
            color: #666;
        }

        .item-products {
            margin-bottom: 15px;
        }

        .products-header {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .product-item {
            background: white;
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 4px;
            font-size: 13px;
            border-left: 3px solid #4a90e2;
        }

        .item-totals {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }

        .totals-left {
            font-size: 14px;
            color: #666;
        }

        .totals-right {
            font-weight: bold;
            color: #4a90e2;
            font-size: 16px;
        }

        .convert-btn {
            background-color: #4a90e2;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }

        .convert-btn:hover {
            background-color: #81b0e6;
        }

        .convert-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .edit-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            min-width: 70px;
        }

        .edit-btn:hover {
            background-color: #0056b3;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            min-width: 70px;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .edit-btn:disabled,
        .delete-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }

        /* Toast styling matching main website */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .toast {
            background-color: #333;
            color: #fff;
            padding: 10px 15px;
            border-radius: 5px;
            opacity: 0.95;
            font-size: 14px;
            animation: fadeInOut 3s forwards;
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; }
            10% { opacity: 0.95; }
            90% { opacity: 0.95; }
            100% { opacity: 0; }
        }

        /* Admin navigation button styles matching main website */
        .admin-nav-button {
            background-color: #9b59b6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s ease;
            margin-bottom: 20px;
        }

        .admin-nav-button:hover {
            background-color: #8e44ad;
            color: white;
        }

        /* Responsive Design matching main website */
        @media (max-width: 830px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px;
            }

            .items-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 15px;
            }

            .item-details {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 650px) {
            body {
                padding: 5px;
            }

            .container {
                padding: 12px;
            }

            .items-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .item-totals {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .admin-nav-button {
                width: 100%;
                text-align: center;
                margin-bottom: 15px;
            }
        }

        /* Edit Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: none;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .modal-header h2 {
            margin: 0;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .btn-save {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .btn-save:hover {
            background-color: #218838;
        }

        .btn-cancel {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .btn-cancel:hover {
            background-color: #545b62;
        }

        @media (max-width: 480px) {
            body {
                padding: 0;
            }

            .container {
                padding: 10px;
            }

            .items-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .item-card {
                padding: 12px;
            }

            .convert-btn {
                width: 100%;
                margin-right: 0;
            }

            .item-details {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .product-item {
                font-size: 12px;
                padding: 6px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 15px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .edit-btn,
            .delete-btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1 style="margin: 0;">Convert to Receipt</h1>
            <div>
                <a href="index.html" class="admin-nav-button">← Back to Dashboard</a>
            </div>
        </div>

        <p style="color: #666; margin-bottom: 30px;">Select quotations or invoices below to convert them into receipts. Once converted, the original quotation/invoice will be removed from the system.</p>

        <!-- Quotations Section -->
        <div class="section">
            <h2>📋 Quotations</h2>
            <div id="quotations-loading" class="loading">
                Loading quotations...
            </div>
            <div id="quotations-container" class="items-grid">
                <!-- Quotations will be loaded here -->
            </div>
            <div id="quotations-empty" class="empty-state" style="display: none;">
                <i>📋</i>
                <h3>No Quotations Found</h3>
                <p>There are no quotations available for conversion.</p>
            </div>
        </div>

        <!-- Invoices Section -->
        <div class="section">
            <h2>🧾 Invoices</h2>
            <div id="invoices-loading" class="loading">
                Loading invoices...
            </div>
            <div id="invoices-container" class="items-grid">
                <!-- Invoices will be loaded here -->
            </div>
            <div id="invoices-empty" class="empty-state" style="display: none;">
                <i>🧾</i>
                <h3>No Invoices Found</h3>
                <p>There are no invoices available for conversion.</p>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Edit Item</h2>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <form id="editForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="editReference">Reference Number:</label>
                        <input type="text" id="editReference" name="reference_number" required>
                    </div>
                    <div class="form-group">
                        <label for="editDate">Date:</label>
                        <input type="date" id="editDate" name="date" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="editBillingName">Customer Name:</label>
                        <input type="text" id="editBillingName" name="billing_name" required>
                    </div>
                    <div class="form-group">
                        <label for="editBillingEmail">Customer Email:</label>
                        <input type="email" id="editBillingEmail" name="billing_email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="editBillingAddress">Billing Address:</label>
                    <textarea id="editBillingAddress" name="billing_address"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="editBillingPhone">Phone:</label>
                        <input type="tel" id="editBillingPhone" name="billing_phone">
                    </div>
                    <div class="form-group">
                        <label for="editPaymentMethod">Payment Method:</label>
                        <select id="editPaymentMethod" name="payment_method" required>
                            <option value="Cash">Cash</option>
                            <option value="Card">Card</option>
                            <option value="EFT">EFT</option>
                            <option value="Cheque">Cheque</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="editSalesperson">Salesperson:</label>
                    <input type="text" id="editSalesperson" name="salesperson_name">
                </div>

                <div class="form-group">
                    <label for="editComments">Comments:</label>
                    <textarea id="editComments" name="comments"></textarea>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn-cancel" onclick="closeEditModal()">Cancel</button>
                    <button type="submit" class="btn-save">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Toast notification function matching main website
        function showToastMessage(message, type = 'success') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            toastContainer.appendChild(toast);

            // Auto-remove after animation completes
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // Format currency
        function formatCurrency(amount) {
            return `R${parseFloat(amount).toFixed(2)}`;
        }

        // Format date
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        // Create item card HTML
        function createItemCard(item, type) {
            const itemId = type === 'quotation' ? item.quotation_id : item.invoice_id;
            
            return `
                <div class="item-card">
                    <div class="item-header">
                        <div class="item-reference">${item.reference_number}</div>
                        <div class="item-date">${formatDate(item.date)}</div>
                    </div>
                    
                    <div class="item-details">
                        <div class="detail-item">
                            <div class="detail-label">Customer:</div>
                            <div class="detail-value">${item.billing_name}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Email:</div>
                            <div class="detail-value">${item.billing_email}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Payment Method:</div>
                            <div class="detail-value">${item.payment_method}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Salesperson:</div>
                            <div class="detail-value">${item.salesperson_name || 'N/A'}</div>
                        </div>
                    </div>

                    <div class="item-products">
                        <div class="products-header">Products (${item.items.length} items):</div>
                        ${item.items.map(product => `
                            <div class="product-item">
                                ${product.item_name} - Qty: ${product.quantity} × ${formatCurrency(product.unit_price_excluding_tax)} = ${formatCurrency(product.total_price)}
                            </div>
                        `).join('')}
                    </div>

                    <div class="item-totals">
                        <div class="totals-left">
                            Subtotal: ${formatCurrency(item.subtotal)} | Tax: ${formatCurrency(item.tax)}
                        </div>
                        <div class="totals-right">
                            Total: ${formatCurrency(item.total)}
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="convert-btn" onclick="convertToReceipt('${type}', ${itemId})">
                            Convert to Receipt
                        </button>
                        <button class="edit-btn" onclick="editItem('${type}', ${itemId})">
                            ✏️ Edit
                        </button>
                        <button class="delete-btn" onclick="deleteItem('${type}', ${itemId})">
                            🗑️ Delete
                        </button>
                    </div>
                </div>
            `;
        }

        // Load quotations
        async function loadQuotations() {
            try {
                const response = await fetch('http://localhost:8000/api/quotations');
                const quotations = await response.json();
                
                const container = document.getElementById('quotations-container');
                const loading = document.getElementById('quotations-loading');
                const empty = document.getElementById('quotations-empty');
                
                loading.style.display = 'none';
                
                if (quotations.length === 0) {
                    empty.style.display = 'block';
                } else {
                    container.innerHTML = quotations.map(q => createItemCard(q, 'quotation')).join('');
                }
            } catch (error) {
                console.error('Error loading quotations:', error);
                document.getElementById('quotations-loading').innerHTML = 'Error loading quotations';
            }
        }

        // Load invoices
        async function loadInvoices() {
            try {
                const response = await fetch('http://localhost:8000/api/invoices');
                const invoices = await response.json();
                
                const container = document.getElementById('invoices-container');
                const loading = document.getElementById('invoices-loading');
                const empty = document.getElementById('invoices-empty');
                
                loading.style.display = 'none';
                
                if (invoices.length === 0) {
                    empty.style.display = 'block';
                } else {
                    container.innerHTML = invoices.map(i => createItemCard(i, 'invoice')).join('');
                }
            } catch (error) {
                console.error('Error loading invoices:', error);
                document.getElementById('invoices-loading').innerHTML = 'Error loading invoices';
            }
        }

        // Convert to receipt
        async function convertToReceipt(type, id) {
            const button = event.target;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.textContent = 'Converting...';
            
            try {
                const endpoint = type === 'quotation'
                    ? `http://localhost:8000/api/convert-quotation-to-receipt/${id}`
                    : `http://localhost:8000/api/convert-invoice-to-receipt/${id}`;
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showToastMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} converted to receipt successfully!`, 'success');
                    // Reload the appropriate section
                    if (type === 'quotation') {
                        loadQuotations();
                    } else {
                        loadInvoices();
                    }
                } else {
                    throw new Error(result.message || 'Conversion failed');
                }
            } catch (error) {
                console.error('Error converting to receipt:', error);
                showToastMessage(`Error: ${error.message}`, 'error');
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // Delete item
        async function deleteItem(type, id) {
            if (!confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.`)) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = 'Deleting...';

            try {
                const endpoint = type === 'quotation'
                    ? `http://localhost:8000/api/quotations/${id}`
                    : `http://localhost:8000/api/invoices/${id}`;

                const response = await fetch(endpoint, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    showToastMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`, 'success');
                    // Reload the appropriate section
                    if (type === 'quotation') {
                        loadQuotations();
                    } else {
                        loadInvoices();
                    }
                } else {
                    throw new Error(result.message || 'Delete failed');
                }
            } catch (error) {
                console.error('Error deleting item:', error);
                showToastMessage(`Error: ${error.message}`, 'error');
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // Edit item
        let currentEditType = '';
        let currentEditId = '';

        async function editItem(type, id) {
            currentEditType = type;
            currentEditId = id;

            try {
                // Fetch current item data
                const endpoint = type === 'quotation'
                    ? `http://localhost:8000/api/quotations/${id}`
                    : `http://localhost:8000/api/invoices/${id}`;

                const response = await fetch(endpoint);

                if (!response.ok) {
                    throw new Error('Item not found');
                }

                const item = await response.json();

                // Populate form with current data
                document.getElementById('modalTitle').textContent = `Edit ${type.charAt(0).toUpperCase() + type.slice(1)}`;
                document.getElementById('editReference').value = item.reference_number || '';
                document.getElementById('editDate').value = item.date || '';
                document.getElementById('editBillingName').value = item.billing_name || '';
                document.getElementById('editBillingEmail').value = item.billing_email || '';
                document.getElementById('editBillingAddress').value = item.billing_address || '';
                document.getElementById('editBillingPhone').value = item.billing_phone || '';
                document.getElementById('editPaymentMethod').value = item.payment_method || '';
                document.getElementById('editSalesperson').value = item.salesperson_name || '';
                document.getElementById('editComments').value = item.comments || '';

                // Show modal
                document.getElementById('editModal').style.display = 'block';

            } catch (error) {
                console.error('Error loading item for edit:', error);
                showToastMessage('Error loading item data', 'error');
            }
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            currentEditType = '';
            currentEditId = '';
        }

        // Handle edit form submission
        document.getElementById('editForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            try {
                // First, get the current item data to preserve products and totals
                const currentEndpoint = currentEditType === 'quotation'
                    ? `http://localhost:8000/api/quotations/${currentEditId}`
                    : `http://localhost:8000/api/invoices/${currentEditId}`;

                const currentResponse = await fetch(currentEndpoint);
                const currentItem = await currentResponse.json();

                const formData = new FormData(e.target);
                const updateData = {};

                for (let [key, value] of formData.entries()) {
                    updateData[key] = value;
                }

                // Preserve existing products and totals
                updateData.subtotal = currentItem.subtotal;
                updateData.tax = currentItem.tax;
                updateData.total = currentItem.total;
                updateData.products = currentItem.items || [];
                updateData.company_name = currentItem.company_name;

                // Update the item
                const updateEndpoint = currentEditType === 'quotation'
                    ? `http://localhost:8000/api/quotations/${currentEditId}`
                    : `http://localhost:8000/api/invoices/${currentEditId}`;

                const response = await fetch(updateEndpoint, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();

                if (response.ok) {
                    showToastMessage(`${currentEditType.charAt(0).toUpperCase() + currentEditType.slice(1)} updated successfully!`, 'success');
                    closeEditModal();

                    // Reload the appropriate section
                    if (currentEditType === 'quotation') {
                        loadQuotations();
                    } else {
                        loadInvoices();
                    }
                } else {
                    throw new Error(result.message || 'Update failed');
                }
            } catch (error) {
                console.error('Error updating item:', error);
                showToastMessage(`Error: ${error.message}`, 'error');
            }
        });

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadQuotations();
            loadInvoices();
        });
    </script>
</body>
</html>
