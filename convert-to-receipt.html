<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#ffffff">
    <meta name="format-detection" content="telephone=no">
    <title>Convert to Receipt - Shans System</title>
    <script src="auth-utils.js"></script>
    <style>
        /* Base styling matching main website */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            backface-visibility: hidden;
            transform: translateZ(0);
            will-change: transform;
        }

        input, select, textarea, button {
            -webkit-appearance: none;
            appearance: none;
            user-select: text;
            -webkit-user-select: text;
        }

        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            max-width: 800px;
            margin: auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        h1, h2 {
            color: #333;
        }

        .section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }

        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .item-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            background: #f9f9f9;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            height: fit-content;
            display: flex;
            flex-direction: column;
        }

        .item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .item-reference {
            font-weight: bold;
            color: #4a90e2;
            font-size: 16px;
        }

        .item-date {
            color: #666;
            font-size: 14px;
        }

        .item-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .detail-item {
            font-size: 14px;
        }

        .detail-label {
            font-weight: bold;
            color: #333;
        }

        .detail-value {
            color: #666;
        }

        .item-products {
            margin-bottom: 15px;
        }

        .products-header {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .product-item {
            background: white;
            padding: 8px;
            margin-bottom: 5px;
            border-radius: 4px;
            font-size: 13px;
            border-left: 3px solid #4a90e2;
        }

        .item-totals {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 4px;
        }

        .totals-left {
            font-size: 14px;
            color: #666;
        }

        .totals-right {
            font-weight: bold;
            color: #4a90e2;
            font-size: 16px;
        }

        .convert-btn {
            background-color: #4a90e2;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            transition: background-color 0.3s ease;
        }

        .convert-btn:hover {
            background-color: #81b0e6;
        }

        .convert-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .edit-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            min-width: 70px;
        }

        .edit-btn:hover {
            background-color: #0056b3;
        }

        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s ease;
            min-width: 70px;
        }

        .delete-btn:hover {
            background-color: #c82333;
        }

        .edit-btn:disabled,
        .delete-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }

        /* Toast styling matching main website */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .toast {
            background-color: #333;
            color: #fff;
            padding: 10px 15px;
            border-radius: 5px;
            opacity: 0.95;
            font-size: 14px;
            animation: fadeInOut 3s forwards;
        }

        .toast.success {
            background-color: #28a745;
        }

        .toast.error {
            background-color: #dc3545;
        }

        @keyframes fadeInOut {
            0% { opacity: 0; }
            10% { opacity: 0.95; }
            90% { opacity: 0.95; }
            100% { opacity: 0; }
        }

        /* Admin navigation button styles matching main website */
        .admin-nav-button {
            background-color: #9b59b6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s ease;
            margin-bottom: 20px;
        }

        .admin-nav-button:hover {
            background-color: #8e44ad;
            color: white;
        }

        /* Responsive Design matching main website */
        @media (max-width: 830px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 15px;
            }

            .items-grid {
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 15px;
            }

            .item-details {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 650px) {
            body {
                padding: 5px;
            }

            .container {
                padding: 12px;
            }

            .items-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .item-totals {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .admin-nav-button {
                width: 100%;
                text-align: center;
                margin-bottom: 15px;
            }
        }

        /* Edit Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fefefe;
            margin: 5% auto;
            padding: 20px;
            border: none;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .modal-header h2 {
            margin: 0;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .btn-save {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .btn-save:hover {
            background-color: #218838;
        }

        .btn-cancel {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .btn-cancel:hover {
            background-color: #545b62;
        }

        .customer-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .customer-info h3 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .customer-info p {
            margin: 0;
            color: #666;
        }

        .products-section {
            margin-bottom: 20px;
        }

        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .products-header h3 {
            margin: 0;
        }

        .btn-add-product {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-add-product:hover {
            background-color: #0056b3;
        }

        .product-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 10px;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            background-color: #fff;
        }

        .product-row input, .product-row select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn-remove-product {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn-remove-product:hover {
            background-color: #c82333;
        }

        .totals-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .totals-row.total-row {
            font-weight: bold;
            font-size: 16px;
            border-top: 1px solid #ddd;
            padding-top: 8px;
            margin-top: 8px;
        }

        /* Edit Modal Specific Styles */
        .edit-modal-content {
            max-width: 90%;
            width: 1000px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .edit-section {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            background-color: white;
        }

        .edit-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
        }

        /* Copy table styles from index.html */
        .table-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            overflow-x: auto;
            margin-bottom: 20px;
        }

        .div-table {
            display: flex;
            flex-direction: column;
            width: 100%;
            background-color: white;
            font-size: 14px;
            border: 1px solid #e0e0e0;
            margin-bottom: 0;
        }

        .div-table-row {
            display: flex;
            flex-direction: row;
            border-bottom: 1px solid #e0e0e0;
        }

        .div-table-row:nth-child(even) {
            background-color: #f9f9f9;
        }

        .div-table-row:hover {
            background-color: #f0f7ff;
        }

        .div-table-header {
            background-color: #f2f2f2;
            font-weight: bold;
            border-bottom: 2px solid #d0d0d0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .div-table-cell {
            padding: 8px 10px;
            border-right: 1px solid #e0e0e0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
        }

        .div-table-cell:last-child {
            border-right: none;
        }

        .div-table-heading {
            color: #333;
            font-weight: 700;
        }

        .div-table-body {
            display: flex;
            flex-direction: column;
        }

        /* Column styling */
        .product-column {
            flex: 3;
            min-width: 200px;
        }

        .qty-column {
            flex: 1;
            min-width: 60px;
            text-align: center;
        }

        .price-column {
            flex: 1.5;
            min-width: 80px;
            text-align: right;
        }

        .tax-column {
            display: flex;
        }

        /* Edit Modal Input Styling */
        .edit-modal-content input, .edit-modal-content select {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .edit-modal-content input[type="checkbox"] {
            width: 18px;
            height: 18px;
            padding: 0;
            margin: 0;
            margin-right: 10px;
            vertical-align: middle;
            cursor: pointer;
            appearance: auto;
            -webkit-appearance: checkbox;
            -moz-appearance: checkbox;
        }

        .edit-modal-content .checkbox-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .edit-modal-content .checkbox-container label {
            margin: 0;
            cursor: pointer;
            user-select: none;
        }

        .edit-modal-content label {
            display: block;
            margin-bottom: 5px;
        }

        /* Product search styling */
        #editProductSearch {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        #editProductList {
            list-style: none;
            padding: 0;
            margin: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            position: absolute;
            z-index: 1000;
            width: 100%;
        }

        #editProductList li {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }

        #editProductList li:hover {
            background-color: #f0f7ff;
        }

        .loader {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: none;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .remove-x {
            color: #dc3545;
            cursor: pointer;
            font-weight: bold;
            font-size: 16px;
        }

        .remove-x:hover {
            color: #c82333;
        }

        .totals-display {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .totals-display .totals-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .totals-display .total-row {
            font-weight: bold;
            font-size: 16px;
            border-top: 1px solid #ddd;
            padding-top: 8px;
            margin-top: 8px;
        }

        @media (max-width: 480px) {
            body {
                padding: 0;
            }

            .container {
                padding: 10px;
            }

            .items-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .item-card {
                padding: 12px;
            }

            .convert-btn {
                width: 100%;
                margin-right: 0;
            }

            .item-details {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .product-item {
                font-size: 12px;
                padding: 6px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 15px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                flex-direction: column;
            }

            .edit-btn,
            .delete-btn {
                width: 100%;
            }

            .product-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .products-header {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .btn-add-product {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Toast Container -->
    <div class="toast-container" id="toastContainer"></div>

    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1 style="margin: 0;">Convert to Receipt</h1>
            <div>
                <a href="index.html" class="admin-nav-button">← Back to Dashboard</a>
            </div>
        </div>

        <p style="color: #666; margin-bottom: 30px;">Select quotations or invoices below to convert them into receipts. Once converted, the original quotation/invoice will be removed from the system.</p>

        <!-- Quotations Section -->
        <div class="section">
            <h2>📋 Quotations</h2>
            <div id="quotations-loading" class="loading">
                Loading quotations...
            </div>
            <div id="quotations-container" class="items-grid">
                <!-- Quotations will be loaded here -->
            </div>
            <div id="quotations-empty" class="empty-state" style="display: none;">
                <i>📋</i>
                <h3>No Quotations Found</h3>
                <p>There are no quotations available for conversion.</p>
            </div>
        </div>

        <!-- Invoices Section -->
        <div class="section">
            <h2>🧾 Invoices</h2>
            <div id="invoices-loading" class="loading">
                Loading invoices...
            </div>
            <div id="invoices-container" class="items-grid">
                <!-- Invoices will be loaded here -->
            </div>
            <div id="invoices-empty" class="empty-state" style="display: none;">
                <i>🧾</i>
                <h3>No Invoices Found</h3>
                <p>There are no invoices available for conversion.</p>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content edit-modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Edit Products</h2>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>

            <div class="customer-info">
                <h3>Customer: <span id="customerName"></span></h3>
                <p>Reference: <span id="referenceNumber"></span></p>
            </div>

            <!-- Add Products Section -->
            <div class="edit-section">
                <h3>Add Products</h3>
                <label for="editProductSearch">Search Products:</label>
                <input type="text" id="editProductSearch" placeholder="Type to search products or enter new product">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <ul id="editProductList"></ul>
                    <div class="loader" id="editSearchLoader"></div>
                </div>

                <label for="editQuantity">Quantity:</label>
                <div>
                    <input type="number" id="editQuantity" value="1" min="1">
                </div>

                <label for="editPrice">Price (R):</label>
                <input type="number" id="editPrice" value="0" min="0" step="1">

                <label for="editRoom" class="hidden" id="editRoomLabel">Room:</label>
                <input type="text" id="editRoom" class="hidden" placeholder="Select a product to see the room" readonly>

                <button type="button" onclick="addEditSelectedProduct()" style="margin-top: 10px; padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Add Product</button>

                <!-- Tax Checkbox -->
                <div class="checkbox-container" style="margin-top: 15px;">
                    <input type="checkbox" id="editTaxCheckbox">
                    <label for="editTaxCheckbox">Add Tax</label>
                </div>
            </div>

            <!-- Selected Products Table -->
            <div class="edit-section">
                <h3>Selected Products</h3>
                <div class="table-container">
                    <div class="div-table">
                        <div class="div-table-row div-table-header" id="editTableHeader">
                            <!-- Header cells will be added dynamically -->
                        </div>
                        <div class="div-table-body" id="editSelectedProductsBody">
                            <!-- Product rows will be added dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Totals Section -->
            <div class="edit-section">
                <div class="totals-display">
                    <div class="totals-row">
                        <span>Subtotal:</span>
                        <span id="editSubtotalAmount">R0</span>
                    </div>
                    <div class="totals-row">
                        <span>Tax (15%):</span>
                        <span id="editTaxAmount">R0</span>
                    </div>
                    <div class="totals-row total-row">
                        <span>Total:</span>
                        <span id="editTotalAmount">R0</span>
                    </div>
                </div>
            </div>

            <div class="modal-actions">
                <button type="button" class="btn-cancel" onclick="closeEditModal()">Cancel</button>
                <button type="button" class="btn-save" onclick="saveEditChanges()">Save Changes</button>
            </div>
        </div>
    </div>

    <script>
        // Toast notification function matching main website
        function showToastMessage(message, type = 'success') {
            const toastContainer = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            toastContainer.appendChild(toast);

            // Auto-remove after animation completes
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        // Format currency
        function formatCurrency(amount) {
            return `R${parseFloat(amount).toFixed(2)}`;
        }

        // Format date
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString();
        }

        // Create item card HTML
        function createItemCard(item, type) {
            const itemId = type === 'quotation' ? item.quotation_id : item.invoice_id;
            
            return `
                <div class="item-card">
                    <div class="item-header">
                        <div class="item-reference">${item.reference_number}</div>
                        <div class="item-date">${formatDate(item.date)}</div>
                    </div>
                    
                    <div class="item-details">
                        <div class="detail-item">
                            <div class="detail-label">Customer:</div>
                            <div class="detail-value">${item.billing_name}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Email:</div>
                            <div class="detail-value">${item.billing_email}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Payment Method:</div>
                            <div class="detail-value">${item.payment_method}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">Salesperson:</div>
                            <div class="detail-value">${item.salesperson_name || 'N/A'}</div>
                        </div>
                    </div>

                    <div class="item-products">
                        <div class="products-header">Products (${item.items.length} items):</div>
                        ${item.items.map(product => `
                            <div class="product-item">
                                ${product.item_name} - Qty: ${product.quantity} × ${formatCurrency(product.unit_price_excluding_tax)} = ${formatCurrency(product.total_price)}
                            </div>
                        `).join('')}
                    </div>

                    <div class="item-totals">
                        <div class="totals-left">
                            Subtotal: ${formatCurrency(item.subtotal)} | Tax: ${formatCurrency(item.tax)}
                        </div>
                        <div class="totals-right">
                            Total: ${formatCurrency(item.total)}
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="convert-btn" onclick="convertToReceipt('${type}', ${itemId})">
                            Convert to Receipt
                        </button>
                        <button class="edit-btn" onclick="editItem('${type}', ${itemId})">
                            ✏️ Edit
                        </button>
                        <button class="delete-btn" onclick="deleteItem('${type}', ${itemId})">
                            🗑️ Delete
                        </button>
                    </div>
                </div>
            `;
        }

        // Load quotations
        async function loadQuotations() {
            try {
                const response = await fetch('http://localhost:8000/api/quotations');
                const quotations = await response.json();
                
                const container = document.getElementById('quotations-container');
                const loading = document.getElementById('quotations-loading');
                const empty = document.getElementById('quotations-empty');
                
                loading.style.display = 'none';
                
                if (quotations.length === 0) {
                    empty.style.display = 'block';
                } else {
                    container.innerHTML = quotations.map(q => createItemCard(q, 'quotation')).join('');
                }
            } catch (error) {
                console.error('Error loading quotations:', error);
                document.getElementById('quotations-loading').innerHTML = 'Error loading quotations';
            }
        }

        // Load invoices
        async function loadInvoices() {
            try {
                const response = await fetch('http://localhost:8000/api/invoices');
                const invoices = await response.json();
                
                const container = document.getElementById('invoices-container');
                const loading = document.getElementById('invoices-loading');
                const empty = document.getElementById('invoices-empty');
                
                loading.style.display = 'none';
                
                if (invoices.length === 0) {
                    empty.style.display = 'block';
                } else {
                    container.innerHTML = invoices.map(i => createItemCard(i, 'invoice')).join('');
                }
            } catch (error) {
                console.error('Error loading invoices:', error);
                document.getElementById('invoices-loading').innerHTML = 'Error loading invoices';
            }
        }

        // Convert to receipt
        async function convertToReceipt(type, id) {
            const button = event.target;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.textContent = 'Converting...';
            
            try {
                const endpoint = type === 'quotation'
                    ? `http://localhost:8000/api/convert-quotation-to-receipt/${id}`
                    : `http://localhost:8000/api/convert-invoice-to-receipt/${id}`;
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showToastMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} converted to receipt successfully!`, 'success');
                    // Reload the appropriate section
                    if (type === 'quotation') {
                        loadQuotations();
                    } else {
                        loadInvoices();
                    }
                } else {
                    throw new Error(result.message || 'Conversion failed');
                }
            } catch (error) {
                console.error('Error converting to receipt:', error);
                showToastMessage(`Error: ${error.message}`, 'error');
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // Delete item
        async function deleteItem(type, id) {
            if (!confirm(`Are you sure you want to delete this ${type}? This action cannot be undone.`)) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = 'Deleting...';

            try {
                const endpoint = type === 'quotation'
                    ? `http://localhost:8000/api/quotations/${id}`
                    : `http://localhost:8000/api/invoices/${id}`;

                const response = await fetch(endpoint, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok) {
                    showToastMessage(`${type.charAt(0).toUpperCase() + type.slice(1)} deleted successfully!`, 'success');
                    // Reload the appropriate section
                    if (type === 'quotation') {
                        loadQuotations();
                    } else {
                        loadInvoices();
                    }
                } else {
                    throw new Error(result.message || 'Delete failed');
                }
            } catch (error) {
                console.error('Error deleting item:', error);
                showToastMessage(`Error: ${error.message}`, 'error');
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // Edit item variables and functions
        let currentEditType = '';
        let currentEditId = '';
        let editSelectedProducts = [];
        let editAllProducts = [];
        let editIsLoading = false;
        let editIsSelectingProduct = false;
        let editOriginalHasTax = false;

        // Edit modal DOM elements
        const editDOM = {
            productSearchInput: null,
            productList: null,
            searchLoader: null,
            roomInput: null,
            roomLabel: null,
            taxCheckbox: null,
            selectedProductsBody: null,
            tableHeader: null,
            priceInput: null,
            quantityInput: null,
            subtotalAmountSpan: null,
            taxAmountSpan: null,
            totalAmountSpan: null
        };

        async function editItem(type, id) {
            currentEditType = type;
            currentEditId = id;

            try {
                // Fetch current item data
                const endpoint = type === 'quotation'
                    ? `http://localhost:8000/api/quotations/${id}`
                    : `http://localhost:8000/api/invoices/${id}`;

                const response = await fetch(endpoint);

                if (!response.ok) {
                    throw new Error('Item not found');
                }

                const item = await response.json();

                // Populate customer info (read-only)
                document.getElementById('modalTitle').textContent = `Edit ${type.charAt(0).toUpperCase() + type.slice(1)} Products`;
                document.getElementById('customerName').textContent = item.billing_name || 'N/A';
                document.getElementById('referenceNumber').textContent = item.reference_number || 'N/A';

                // Determine if original had tax
                editOriginalHasTax = item.tax > 0;

                // Initialize edit DOM elements
                initializeEditDOM();

                // Set tax checkbox to unchecked by default (user can choose)
                editDOM.taxCheckbox.checked = false;

                // Load existing products into edit selected products
                editSelectedProducts = [];
                if (item.items && item.items.length > 0) {
                    item.items.forEach((product, index) => {
                        // Calculate the original full price from the stored data
                        let originalPrice;
                        if (editOriginalHasTax && product.tax_per_product > 0) {
                            // If tax was included, reconstruct the full price
                            originalPrice = product.unit_price_excluding_tax + product.tax_per_product;
                        } else {
                            // No tax, use the unit price as is
                            originalPrice = product.unit_price_excluding_tax;
                        }

                        editSelectedProducts.push({
                            name: product.item_name,
                            quantity: product.quantity,
                            price: originalPrice,
                            item_code: product.item_code || `temp_${index}`,
                            room_id: product.room_id || null,
                            room_name: product.room_name || 'N/A',
                            is_new: false,
                            tax_per_product: product.tax_per_product || 0
                        });
                    });
                }

                // Load all products for search
                await editFetchAllProducts();

                // Update the products table
                updateEditSelectedProductsTable();

                // Show modal
                document.getElementById('editModal').style.display = 'block';

            } catch (error) {
                console.error('Error loading item for edit:', error);
                showToastMessage('Error loading item data', 'error');
            }
        }

        // Initialize edit modal DOM elements
        function initializeEditDOM() {
            editDOM.productSearchInput = document.getElementById('editProductSearch');
            editDOM.productList = document.getElementById('editProductList');
            editDOM.searchLoader = document.getElementById('editSearchLoader');
            editDOM.roomInput = document.getElementById('editRoom');
            editDOM.roomLabel = document.getElementById('editRoomLabel');
            editDOM.taxCheckbox = document.getElementById('editTaxCheckbox');
            editDOM.selectedProductsBody = document.getElementById('editSelectedProductsBody');
            editDOM.tableHeader = document.getElementById('editTableHeader');
            editDOM.priceInput = document.getElementById('editPrice');
            editDOM.quantityInput = document.getElementById('editQuantity');
            editDOM.subtotalAmountSpan = document.getElementById('editSubtotalAmount');
            editDOM.taxAmountSpan = document.getElementById('editTaxAmount');
            editDOM.totalAmountSpan = document.getElementById('editTotalAmount');

            // Add event listeners
            if (!editDOM.productSearchInput.hasEditListeners) {
                editDOM.productSearchInput.addEventListener('input', editDebouncedSearch);
                editDOM.productSearchInput.addEventListener('blur', () => {
                    setTimeout(() => {
                        if (!editIsSelectingProduct) {
                            editDOM.productList.innerHTML = '';
                        }
                    }, 500);
                });
                editDOM.productSearchInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        editDOM.productSearchInput.value = '';
                        editDOM.productList.innerHTML = '';
                    }
                });
                editDOM.productSearchInput.hasEditListeners = true;
            }

            // Add tax checkbox event listener
            if (!editDOM.taxCheckbox.hasEditListeners) {
                editDOM.taxCheckbox.addEventListener('change', () => {
                    updateEditSelectedProductsTable();
                    editCalculateAndDisplayTotal();
                });
                editDOM.taxCheckbox.hasEditListeners = true;
            }
        }

        // Fetch all products for edit modal
        async function editFetchAllProducts() {
            if (editIsLoading) return;

            editIsLoading = true;
            try {
                const response = await fetch('http://localhost:8000/api/products');

                if (!response.ok) {
                    throw new Error('Failed to fetch products');
                }

                const products = await response.json();
                editAllProducts = Array.isArray(products) ? products : [];

            } catch (error) {
                console.error('Error fetching products for edit:', error);
                editAllProducts = [];
            } finally {
                editIsLoading = false;
            }
        }

        // Debounced search for edit modal
        const editDebouncedSearch = debounce(() => {
            editDOM.searchLoader.style.display = 'inline-block';
            editDisplayFilteredProducts();
            editDOM.searchLoader.style.display = 'none';
        }, 300);

        // Display filtered products in edit modal
        function editDisplayFilteredProducts() {
            const searchTerm = editDOM.productSearchInput.value.toLowerCase().trim();

            editDOM.productList.innerHTML = '';

            if (searchTerm.length === 0) {
                return;
            }

            const filteredProducts = editAllProducts.filter(product =>
                product.item_name && product.item_name.toLowerCase().includes(searchTerm)
            );

            if (filteredProducts.length === 0) {
                const li = document.createElement('li');
                li.textContent = editAllProducts.length === 0 ? 'Loading products...' : 'No products found';
                li.style.color = '#666';
                li.style.fontStyle = 'italic';
                editDOM.productList.appendChild(li);
                return;
            }

            filteredProducts.slice(0, 10).forEach(product => {
                const li = document.createElement('li');
                li.textContent = `${product.item_name} - R${Math.round(product.unit_retail_price)}`;
                li.onclick = () => editSelectProduct(product);
                editDOM.productList.appendChild(li);
            });
        }

        // Select product in edit modal
        function editSelectProduct(product) {
            editIsSelectingProduct = true;
            editDOM.productSearchInput.value = product.item_name;
            editDOM.productSearchInput.dataset.itemCode = product.item_code;
            editDOM.productSearchInput.dataset.roomId = product.room_id;
            editDOM.productList.innerHTML = '';
            editDOM.priceInput.value = Math.round(parseFloat(product.unit_retail_price));

            // Show room info if available
            if (product.room_name && product.room_name !== 'N/A') {
                editDOM.roomInput.value = product.room_name;
                editDOM.roomLabel.classList.remove('hidden');
                editDOM.roomInput.classList.remove('hidden');
            } else {
                editDOM.roomLabel.classList.add('hidden');
                editDOM.roomInput.classList.add('hidden');
            }

            setTimeout(() => {
                editIsSelectingProduct = false;
            }, 100);
        }

        // Add selected product in edit modal
        function addEditSelectedProduct() {
            const productName = editDOM.productSearchInput.value.trim();
            const quantity = parseInt(editDOM.quantityInput.value);
            const price = Math.round(parseFloat(editDOM.priceInput.value));

            const itemCode = editDOM.productSearchInput.dataset.itemCode;
            const roomId = editDOM.productSearchInput.dataset.roomId;

            if (!productName || quantity <= 0 || price < 0) {
                showToastMessage('Please fill in all product details correctly', 'error');
                return;
            }

            // Check if product already exists
            const existingIndex = editSelectedProducts.findIndex(p =>
                p.item_code === itemCode || (p.name === productName && !itemCode)
            );

            if (existingIndex !== -1) {
                // Update existing product quantity
                editSelectedProducts[existingIndex].quantity += quantity;
            } else {
                // Add new product
                let product;
                if (itemCode) {
                    product = editAllProducts.find(p => p.item_code === itemCode);
                }

                editSelectedProducts.push({
                    name: productName,
                    quantity: quantity,
                    price: price,
                    item_code: itemCode || `temp_${Date.now()}`,
                    room_id: roomId || null,
                    room_name: product ? product.room_name : 'N/A',
                    is_new: !itemCode
                });
            }

            updateEditSelectedProductsTable();

            // Clear form
            editDOM.productSearchInput.value = '';
            editDOM.productList.innerHTML = '';
            delete editDOM.productSearchInput.dataset.itemCode;
            delete editDOM.productSearchInput.dataset.roomId;
            editDOM.quantityInput.value = '1';
            editDOM.priceInput.value = '0';
            editDOM.roomLabel.classList.add('hidden');
            editDOM.roomInput.classList.add('hidden');
        }

        // Update selected products table in edit modal
        function updateEditSelectedProductsTable() {
            editDOM.selectedProductsBody.innerHTML = '';
            updateEditTableHeader();

            if (editSelectedProducts.length === 0) {
                const emptyRow = document.createElement('div');
                emptyRow.className = 'div-table-row';
                emptyRow.style.justifyContent = 'center';
                emptyRow.style.padding = '20px';
                emptyRow.style.color = '#666';
                emptyRow.innerHTML = '<i>No products selected yet. Search and add products above.</i>';
                editDOM.selectedProductsBody.appendChild(emptyRow);
                editCalculateAndDisplayTotal();
                return;
            }

            const fragment = document.createDocumentFragment();
            editSelectedProducts.forEach((product, index) => {
                const row = editCreateProductRow(product, index);
                fragment.appendChild(row);
            });

            editDOM.selectedProductsBody.appendChild(fragment);
            editCalculateAndDisplayTotal();
        }

        // Create product row for edit modal
        function editCreateProductRow(product, index) {
            const row = document.createElement('div');
            row.className = 'div-table-row';

            // Product name cell
            const nameCell = document.createElement('div');
            nameCell.className = 'div-table-cell product-column';
            let nameContent = product.name;
            if (product.room_name && product.room_name !== 'N/A') {
                nameContent += ` <small style="color: #666;">(${product.room_name})</small>`;
            }
            nameCell.innerHTML = nameContent;
            row.appendChild(nameCell);

            // Quantity cell
            const qtyCell = document.createElement('div');
            qtyCell.className = 'div-table-cell qty-column';
            qtyCell.textContent = product.quantity;
            qtyCell.style.fontWeight = '700';
            qtyCell.style.textAlign = 'center';
            row.appendChild(qtyCell);

            if (editDOM.taxCheckbox.checked) {
                // Price cell (net price when tax is included)
                const priceCell = document.createElement('div');
                priceCell.className = 'div-table-cell price-column';
                const netPrice = product.price * 0.85;
                priceCell.textContent = `R ${Math.round(netPrice)}`;
                row.appendChild(priceCell);

                // Tax cell
                const taxCell = document.createElement('div');
                taxCell.className = 'div-table-cell price-column tax-column';
                const taxPerUnit = product.price * 0.15;
                taxCell.textContent = `R ${Math.round(taxPerUnit)}`;
                row.appendChild(taxCell);

                // Total cell
                const totalCell = document.createElement('div');
                totalCell.className = 'div-table-cell price-column';
                const lineTotal = product.price * product.quantity;
                totalCell.textContent = `R ${Math.round(lineTotal)}`;
                row.appendChild(totalCell);
            } else {
                // Price cell (full price when no tax)
                const priceCell = document.createElement('div');
                priceCell.className = 'div-table-cell price-column';
                priceCell.textContent = `R ${Math.round(product.price)}`;
                row.appendChild(priceCell);

                // Total cell
                const totalCell = document.createElement('div');
                totalCell.className = 'div-table-cell price-column';
                const lineTotal = product.price * product.quantity;
                totalCell.textContent = `R ${Math.round(lineTotal)}`;
                row.appendChild(totalCell);
            }

            // Action cell (remove button)
            const actionCell = document.createElement('div');
            actionCell.className = 'div-table-cell';
            actionCell.style.textAlign = 'center';
            actionCell.style.width = '40px';
            actionCell.innerHTML = `<span class="remove-x" onclick="editRemoveProduct(${index})">✕</span>`;
            row.appendChild(actionCell);

            return row;
        }

        // Update table header for edit modal
        function updateEditTableHeader() {
            const header = editDOM.tableHeader;
            header.innerHTML = '';

            // Product column
            const productHeader = document.createElement('div');
            productHeader.className = 'div-table-cell div-table-heading product-column';
            productHeader.textContent = 'Product';
            header.appendChild(productHeader);

            // QTY column
            const qtyHeader = document.createElement('div');
            qtyHeader.className = 'div-table-cell div-table-heading qty-column';
            qtyHeader.textContent = 'QTY';
            qtyHeader.style.fontWeight = '700';
            qtyHeader.style.textAlign = 'center';
            qtyHeader.style.fontSize = '13px';
            header.appendChild(qtyHeader);

            // Price column
            const priceHeader = document.createElement('div');
            priceHeader.className = 'div-table-cell div-table-heading price-column';
            priceHeader.textContent = 'PRICE';
            priceHeader.style.fontSize = '13px';
            header.appendChild(priceHeader);

            if (editDOM.taxCheckbox.checked) {
                // Tax column (only if tax checkbox is checked)
                const taxHeader = document.createElement('div');
                taxHeader.className = 'div-table-cell div-table-heading price-column tax-column';
                taxHeader.textContent = 'TAX';
                taxHeader.style.fontSize = '13px';
                header.appendChild(taxHeader);
            }

            // Total column
            const totalHeader = document.createElement('div');
            totalHeader.className = 'div-table-cell div-table-heading price-column';
            totalHeader.textContent = 'TOTAL';
            totalHeader.style.fontSize = '13px';
            header.appendChild(totalHeader);

            // Action column
            const actionHeader = document.createElement('div');
            actionHeader.className = 'div-table-cell div-table-heading';
            actionHeader.innerHTML = '&nbsp;';
            actionHeader.style.textAlign = 'center';
            actionHeader.style.width = '40px';
            header.appendChild(actionHeader);
        }

        // Remove product from edit modal
        function editRemoveProduct(index) {
            editSelectedProducts.splice(index, 1);
            updateEditSelectedProductsTable();
        }

        // Calculate and display totals for edit modal
        function editCalculateAndDisplayTotal() {
            let subtotal, tax, total;

            if (editDOM.taxCheckbox.checked) {
                // Calculate with tax (same logic as quotation/invoice pages)
                subtotal = editSelectedProducts.reduce((sum, product) => {
                    const netPrice = product.price * 0.85;
                    return sum + (netPrice * product.quantity);
                }, 0);

                tax = editSelectedProducts.reduce((sum, product) => {
                    const taxPerUnit = product.price * 0.15;
                    return sum + (taxPerUnit * product.quantity);
                }, 0);

                total = editSelectedProducts.reduce((sum, product) => {
                    return sum + (product.price * product.quantity);
                }, 0);
            } else {
                // No tax calculation
                subtotal = editSelectedProducts.reduce((sum, product) => {
                    return sum + (product.price * product.quantity);
                }, 0);

                tax = 0;
                total = subtotal;
            }

            editDOM.subtotalAmountSpan.textContent = formatCurrency(subtotal);
            editDOM.taxAmountSpan.textContent = formatCurrency(tax);
            editDOM.totalAmountSpan.textContent = formatCurrency(total);
        }

        // Close edit modal
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            currentEditType = '';
            currentEditId = '';
            editSelectedProducts = [];
            editAllProducts = [];
            editOriginalHasTax = false;
            if (editDOM.taxCheckbox) {
                editDOM.taxCheckbox.checked = false;
            }
        }

        // Save edit changes
        async function saveEditChanges() {
            if (editSelectedProducts.length === 0) {
                showToastMessage('Please add at least one product', 'error');
                return;
            }

            try {
                // First, get the current item data to preserve all existing fields
                const endpoint = currentEditType === 'quotation'
                    ? `http://localhost:8000/api/quotations/${currentEditId}`
                    : `http://localhost:8000/api/invoices/${currentEditId}`;

                const getResponse = await fetch(endpoint);
                if (!getResponse.ok) {
                    throw new Error('Failed to fetch current item data');
                }

                const currentData = await getResponse.json();

                // Calculate new totals
                let subtotal = 0;
                let tax = 0;
                let total = 0;

                if (editDOM.taxCheckbox.checked) {
                    subtotal = editSelectedProducts.reduce((sum, product) => {
                        const netPrice = product.price * 0.85;
                        return sum + (netPrice * product.quantity);
                    }, 0);

                    tax = editSelectedProducts.reduce((sum, product) => {
                        const taxPerUnit = product.price * 0.15;
                        return sum + (taxPerUnit * product.quantity);
                    }, 0);

                    total = editSelectedProducts.reduce((sum, product) => {
                        return sum + (product.price * product.quantity);
                    }, 0);
                } else {
                    subtotal = editSelectedProducts.reduce((sum, product) => {
                        return sum + (product.price * product.quantity);
                    }, 0);

                    tax = 0;
                    total = subtotal;
                }

                // Prepare products array in the format expected by the backend
                const products = editSelectedProducts.map(product => {
                    if (editDOM.taxCheckbox.checked) {
                        // Calculate tax values
                        const netPrice = Math.round(product.price * 0.85);
                        const taxPerUnit = Math.round(product.price * 0.15);
                        const totalPrice = Math.round(product.price * product.quantity);

                        return {
                            item_code: product.item_code,
                            name: product.name,
                            item_name: product.name,
                            quantity: product.quantity,
                            unitPriceExcludingTax: netPrice,
                            taxPerUnit: taxPerUnit,
                            totalPrice: totalPrice
                        };
                    } else {
                        // No tax
                        return {
                            item_code: product.item_code,
                            name: product.name,
                            item_name: product.name,
                            quantity: product.quantity,
                            unitPriceExcludingTax: product.price,
                            taxPerUnit: 0,
                            totalPrice: product.quantity * product.price
                        };
                    }
                });

                // Prepare complete update data preserving all existing fields
                const updateData = {
                    reference_number: currentData.reference_number,
                    date: currentData.date,
                    billing_name: currentData.billing_name,
                    billing_address: currentData.billing_address,
                    billing_email: currentData.billing_email,
                    billing_phone: currentData.billing_phone,
                    shipping_name: currentData.shipping_name,
                    shipping_address: currentData.shipping_address,
                    shipping_email: currentData.shipping_email,
                    shipping_phone: currentData.shipping_phone,
                    payment_method: currentData.payment_method,
                    subtotal: Math.round(subtotal),
                    tax: Math.round(tax),
                    total: Math.round(total),
                    salesperson_name: currentData.salesperson_name,
                    company_name: currentData.company_name,
                    comments: currentData.comments,
                    products: products
                };

                // Send update request
                const endpoint = currentEditType === 'quotation'
                    ? `http://localhost:8000/api/quotations/${currentEditId}`
                    : `http://localhost:8000/api/invoices/${currentEditId}`;

                const response = await fetch(endpoint, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                if (!response.ok) {
                    const errorData = await response.text();
                    console.error('Update failed:', errorData);
                    throw new Error('Failed to update item');
                }

                showToastMessage(`${currentEditType.charAt(0).toUpperCase() + currentEditType.slice(1)} updated successfully!`, 'success');
                closeEditModal();

                // Refresh the items list
                loadItems();

            } catch (error) {
                console.error('Error updating item:', error);
                showToastMessage('Error updating item', 'error');
            }
        }

        // Debounce function for search
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadQuotations();
            loadInvoices();
        });
    </script>
</body>
</html>
