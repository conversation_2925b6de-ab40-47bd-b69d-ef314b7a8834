// Migration Verification Script
// Verifies the final state of the database after migration and cleanup

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function verifyMigration() {
  console.log('🔍 Verifying database migration and cleanup...');
  
  try {
    const tables = [
      'users', 'rooms', 'expense_categories', 'categories', 'car_brands', 
      'car_models', 'products', 'sales', 'sale_items', 'expenses', 
      'quotations', 'quotation_items', 'invoices', 'invoice_items'
    ];
    
    console.log('\n📊 Final Database State:');
    console.log('========================');
    
    for (const table of tables) {
      try {
        const result = await pool.query(`SELECT COUNT(*) as count FROM ${table}`);
        const count = parseInt(result.rows[0].count);
        console.log(`✅ ${table}: ${count} records`);
      } catch (error) {
        console.log(`❌ ${table}: Error - ${error.message}`);
      }
    }
    
    // Check for any remaining test products
    console.log('\n🔍 Checking for remaining test products...');
    const testProductsQuery = `
      SELECT item_code, item_name 
      FROM products 
      WHERE LOWER(item_name) LIKE '%test%'
      ORDER BY item_code
    `;
    
    const testProductsResult = await pool.query(testProductsQuery);
    
    if (testProductsResult.rows.length === 0) {
      console.log('✅ No test products found - cleanup successful!');
    } else {
      console.log(`⚠️  Found ${testProductsResult.rows.length} products with "test" in name:`);
      testProductsResult.rows.forEach(product => {
        console.log(`  - ${product.item_code}: ${product.item_name}`);
      });
    }
    
    // Check database sequences
    console.log('\n🔧 Checking database sequences...');
    const sequencesQuery = `
      SELECT schemaname, sequencename, last_value 
      FROM pg_sequences 
      WHERE schemaname = 'public'
      ORDER BY sequencename
    `;
    
    const sequencesResult = await pool.query(sequencesQuery);
    sequencesResult.rows.forEach(seq => {
      console.log(`✅ ${seq.sequencename}: next value = ${parseInt(seq.last_value) + 1}`);
    });
    
    console.log('\n🎉 Migration verification completed!');
    
  } catch (error) {
    console.error('❌ Error during verification:', error);
    throw error;
  }
}

async function runVerification() {
  try {
    await verifyMigration();
  } catch (error) {
    console.error('💥 Verification failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  runVerification();
}

module.exports = { verifyMigration };
