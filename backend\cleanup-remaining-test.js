// Clean up remaining test product
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function cleanupRemainingTest() {
  try {
    console.log('🗑️  Cleaning up remaining test product...');
    
    // Check for related records first
    const saleItemsResult = await pool.query('SELECT COUNT(*) as count FROM sale_items WHERE item_code = $1', ['2000111']);
    const quotationItemsResult = await pool.query('SELECT COUNT(*) as count FROM quotation_items WHERE item_code = $1', ['2000111']);
    const invoiceItemsResult = await pool.query('SELECT COUNT(*) as count FROM invoice_items WHERE item_code = $1', ['2000111']);
    
    console.log('Related records:');
    console.log('- Sale items:', saleItemsResult.rows[0].count);
    console.log('- Quotation items:', quotationItemsResult.rows[0].count);
    console.log('- Invoice items:', invoiceItemsResult.rows[0].count);
    
    // Delete the test product
    const result = await pool.query('DELETE FROM products WHERE item_code = $1', ['2000111']);
    console.log('✅ Deleted test product:', result.rowCount, 'record(s)');
    
    await pool.end();
  } catch (error) {
    console.error('❌ Error:', error.message);
    await pool.end();
  }
}

cleanupRemainingTest();
