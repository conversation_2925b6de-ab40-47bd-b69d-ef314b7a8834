// Fix Sales Migration Script
// Handles the foreign key constraint issue with sales table migration

const { Pool } = require('pg');
require('dotenv').config();

// Old database connection (Neon)
const oldDbConfig = {
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
};

// New database connection (Render)
const newDbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
};

const oldPool = new Pool(oldDbConfig);
const newPool = new Pool(newDbConfig);

async function fixSalesMigration() {
  console.log('🔧 Fixing sales table migration...');

  try {
    // First, clear existing sale_items that reference non-existent sales
    console.log('🗑️  Clearing orphaned sale_items...');
    await newPool.query('DELETE FROM sale_items WHERE sale_id NOT IN (SELECT id FROM sales)');

    // Get sales data from old database
    console.log('📊 Fetching sales data from old database...');
    const salesResult = await oldPool.query('SELECT * FROM sales ORDER BY id');
    const salesData = salesResult.rows;

    console.log(`📦 Found ${salesData.length} sales records to migrate`);

    // Insert sales data in batches
    const batchSize = 50;
    let migrated = 0;

    for (let i = 0; i < salesData.length; i += batchSize) {
      const batch = salesData.slice(i, i + batchSize);

      for (const sale of batch) {
        try {
          // Check if sale already exists
          const existingResult = await newPool.query('SELECT id FROM sales WHERE id = $1', [sale.id]);
          if (existingResult.rows.length > 0) {
            console.log(`⏭️  Sale ${sale.id} already exists, skipping...`);
            migrated++;
            continue;
          }

          const query = `
            INSERT INTO sales (
              id, reference_number, date, billing_name, billing_address,
              billing_phone, billing_email, subtotal, tax_amount, total_amount,
              payment_method, notes, created_at, updated_at, company_name,
              salesperson, total_profit
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
          `;

          const values = [
            sale.id, sale.reference_number, sale.date, sale.billing_name,
            sale.billing_address, sale.billing_phone, sale.billing_email,
            sale.subtotal, sale.tax_amount, sale.total_amount, sale.payment_method,
            sale.notes, sale.created_at, sale.updated_at, sale.company_name,
            sale.salesperson, sale.total_profit
          ];

          await newPool.query(query, values);
          migrated++;
        } catch (error) {
          console.error(`❌ Error inserting sale ${sale.id}:`, error.message);
        }
      }

      console.log(`📦 Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(salesData.length/batchSize)} - ${migrated} sales migrated`);
    }

    // Fix the sales sequence
    const maxIdResult = await newPool.query('SELECT MAX(id) as max_id FROM sales');
    const maxId = maxIdResult.rows[0].max_id || 0;
    await newPool.query(`SELECT setval('sales_id_seq', ${maxId + 1})`);

    console.log(`✅ Successfully migrated ${migrated}/${salesData.length} sales records`);
    console.log('✅ Sales sequence fixed');

    return migrated;

  } catch (error) {
    console.error('❌ Error fixing sales migration:', error);
    throw error;
  }
}

async function runFix() {
  try {
    const migrated = await fixSalesMigration();
    console.log(`🎉 Sales migration fix completed! ${migrated} records migrated.`);
  } catch (error) {
    console.error('💥 Sales migration fix failed:', error.message);
    process.exit(1);
  } finally {
    await oldPool.end();
    await newPool.end();
  }
}

if (require.main === module) {
  runFix();
}

module.exports = { fixSalesMigration };
