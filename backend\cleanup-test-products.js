// Test Product Cleanup Script
// Removes all test products named "test 2222" and "test" along with all related records

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function findTestProducts() {
  console.log('🔍 Finding test products...');
  
  try {
    const query = `
      SELECT item_code, item_name 
      FROM products 
      WHERE LOWER(item_name) IN ('test 2222', 'test')
      ORDER BY item_code
    `;
    
    const result = await pool.query(query);
    const testProducts = result.rows;
    
    console.log(`📦 Found ${testProducts.length} test products:`);
    testProducts.forEach(product => {
      console.log(`  - ${product.item_code}: ${product.item_name}`);
    });
    
    return testProducts.map(p => p.item_code);
    
  } catch (error) {
    console.error('❌ Error finding test products:', error);
    throw error;
  }
}

async function findRelatedRecords(testProductCodes) {
  console.log('🔍 Finding related records...');
  
  const relatedData = {
    sale_items: [],
    quotation_items: [],
    invoice_items: [],
    sales: [],
    quotations: [],
    invoices: []
  };
  
  try {
    // Find sale_items with test products
    if (testProductCodes.length > 0) {
      const saleItemsQuery = `
        SELECT id, sale_id, item_code, item_name 
        FROM sale_items 
        WHERE item_code = ANY($1)
        ORDER BY sale_id, id
      `;
      const saleItemsResult = await pool.query(saleItemsQuery, [testProductCodes]);
      relatedData.sale_items = saleItemsResult.rows;
      
      // Find sales that only contain test products
      if (relatedData.sale_items.length > 0) {
        const saleIds = [...new Set(relatedData.sale_items.map(item => item.sale_id))];
        
        for (const saleId of saleIds) {
          const allItemsQuery = `
            SELECT item_code 
            FROM sale_items 
            WHERE sale_id = $1
          `;
          const allItemsResult = await pool.query(allItemsQuery, [saleId]);
          const allItems = allItemsResult.rows.map(row => row.item_code);
          
          // Check if all items in this sale are test products
          const onlyTestProducts = allItems.every(itemCode => testProductCodes.includes(itemCode));
          
          if (onlyTestProducts) {
            relatedData.sales.push(saleId);
          }
        }
      }
      
      // Find quotation_items with test products
      const quotationItemsQuery = `
        SELECT id, quotation_id, item_code, item_name 
        FROM quotation_items 
        WHERE item_code = ANY($1)
        ORDER BY quotation_id, id
      `;
      const quotationItemsResult = await pool.query(quotationItemsQuery, [testProductCodes]);
      relatedData.quotation_items = quotationItemsResult.rows;
      
      // Find quotations that only contain test products
      if (relatedData.quotation_items.length > 0) {
        const quotationIds = [...new Set(relatedData.quotation_items.map(item => item.quotation_id))];
        
        for (const quotationId of quotationIds) {
          const allItemsQuery = `
            SELECT item_code 
            FROM quotation_items 
            WHERE quotation_id = $1
          `;
          const allItemsResult = await pool.query(allItemsQuery, [quotationId]);
          const allItems = allItemsResult.rows.map(row => row.item_code);
          
          const onlyTestProducts = allItems.every(itemCode => testProductCodes.includes(itemCode));
          
          if (onlyTestProducts) {
            relatedData.quotations.push(quotationId);
          }
        }
      }
      
      // Find invoice_items with test products
      const invoiceItemsQuery = `
        SELECT id, invoice_id, item_code, item_name 
        FROM invoice_items 
        WHERE item_code = ANY($1)
        ORDER BY invoice_id, id
      `;
      const invoiceItemsResult = await pool.query(invoiceItemsQuery, [testProductCodes]);
      relatedData.invoice_items = invoiceItemsResult.rows;
      
      // Find invoices that only contain test products
      if (relatedData.invoice_items.length > 0) {
        const invoiceIds = [...new Set(relatedData.invoice_items.map(item => item.invoice_id))];
        
        for (const invoiceId of invoiceIds) {
          const allItemsQuery = `
            SELECT item_code 
            FROM invoice_items 
            WHERE invoice_id = $1
          `;
          const allItemsResult = await pool.query(allItemsQuery, [invoiceId]);
          const allItems = allItemsResult.rows.map(row => row.item_code);
          
          const onlyTestProducts = allItems.every(itemCode => testProductCodes.includes(itemCode));
          
          if (onlyTestProducts) {
            relatedData.invoices.push(invoiceId);
          }
        }
      }
    }
    
    console.log('📊 Related records found:');
    console.log(`  - Sale items: ${relatedData.sale_items.length}`);
    console.log(`  - Sales (only test products): ${relatedData.sales.length}`);
    console.log(`  - Quotation items: ${relatedData.quotation_items.length}`);
    console.log(`  - Quotations (only test products): ${relatedData.quotations.length}`);
    console.log(`  - Invoice items: ${relatedData.invoice_items.length}`);
    console.log(`  - Invoices (only test products): ${relatedData.invoices.length}`);
    
    return relatedData;
    
  } catch (error) {
    console.error('❌ Error finding related records:', error);
    throw error;
  }
}

async function deleteTestData(testProductCodes, relatedData) {
  console.log('🗑️  Starting test data deletion...');
  
  try {
    let deletedCount = 0;
    
    // Delete invoice items first
    if (relatedData.invoice_items.length > 0) {
      const invoiceItemIds = relatedData.invoice_items.map(item => item.id);
      await pool.query('DELETE FROM invoice_items WHERE id = ANY($1)', [invoiceItemIds]);
      console.log(`✅ Deleted ${invoiceItemIds.length} invoice items`);
      deletedCount += invoiceItemIds.length;
    }
    
    // Delete invoices that only contained test products
    if (relatedData.invoices.length > 0) {
      await pool.query('DELETE FROM invoices WHERE id = ANY($1)', [relatedData.invoices]);
      console.log(`✅ Deleted ${relatedData.invoices.length} invoices`);
      deletedCount += relatedData.invoices.length;
    }
    
    // Delete quotation items
    if (relatedData.quotation_items.length > 0) {
      const quotationItemIds = relatedData.quotation_items.map(item => item.id);
      await pool.query('DELETE FROM quotation_items WHERE id = ANY($1)', [quotationItemIds]);
      console.log(`✅ Deleted ${quotationItemIds.length} quotation items`);
      deletedCount += quotationItemIds.length;
    }
    
    // Delete quotations that only contained test products
    if (relatedData.quotations.length > 0) {
      await pool.query('DELETE FROM quotations WHERE id = ANY($1)', [relatedData.quotations]);
      console.log(`✅ Deleted ${relatedData.quotations.length} quotations`);
      deletedCount += relatedData.quotations.length;
    }
    
    // Delete sale items
    if (relatedData.sale_items.length > 0) {
      const saleItemIds = relatedData.sale_items.map(item => item.id);
      await pool.query('DELETE FROM sale_items WHERE id = ANY($1)', [saleItemIds]);
      console.log(`✅ Deleted ${saleItemIds.length} sale items`);
      deletedCount += saleItemIds.length;
    }
    
    // Delete sales that only contained test products
    if (relatedData.sales.length > 0) {
      await pool.query('DELETE FROM sales WHERE id = ANY($1)', [relatedData.sales]);
      console.log(`✅ Deleted ${relatedData.sales.length} sales`);
      deletedCount += relatedData.sales.length;
    }
    
    // Finally, delete the test products themselves
    if (testProductCodes.length > 0) {
      await pool.query('DELETE FROM products WHERE item_code = ANY($1)', [testProductCodes]);
      console.log(`✅ Deleted ${testProductCodes.length} test products`);
      deletedCount += testProductCodes.length;
    }
    
    console.log(`🎉 Total records deleted: ${deletedCount}`);
    
  } catch (error) {
    console.error('❌ Error deleting test data:', error);
    throw error;
  }
}

async function runCleanup() {
  try {
    console.log('🚀 Starting test product cleanup...');
    
    const testProductCodes = await findTestProducts();
    
    if (testProductCodes.length === 0) {
      console.log('✅ No test products found. Cleanup complete!');
      return;
    }
    
    const relatedData = await findRelatedRecords(testProductCodes);
    
    // Ask for confirmation (in a real scenario)
    console.log('\n⚠️  WARNING: This will permanently delete all test products and related records!');
    
    await deleteTestData(testProductCodes, relatedData);
    
    console.log('🎉 Test product cleanup completed successfully!');
    
  } catch (error) {
    console.error('💥 Test product cleanup failed:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  runCleanup();
}

module.exports = { runCleanup };
