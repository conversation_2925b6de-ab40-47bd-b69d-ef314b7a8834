// models/quotationModel.js
const pool = require('../database');

const quotationModel = {
  createQuotation: async (quotationData) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Insert quotation record
      const quotationQuery = `
        INSERT INTO quotations (
          reference_number, date, billing_name, billing_address,
          billing_email, billing_phone, shipping_name, shipping_address,
          shipping_email, shipping_phone, payment_method, subtotal,
          tax, total, salesperson_name, company_name, comments
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
        RETURNING id
      `;

      const quotationValues = [
        quotationData.reference_number,
        quotationData.date,
        quotationData.billing_name,
        quotationData.billing_address,
        quotationData.billing_email,
        quotationData.billing_phone,
        quotationData.shipping_name,
        quotationData.shipping_address,
        quotationData.shipping_email,
        quotationData.shipping_phone,
        quotationData.payment_method,
        quotationData.subtotal,
        quotationData.tax,
        quotationData.total,
        quotationData.salesperson_name || '',
        quotationData.company_name || 'Shans Accessories PTY LTD',
        quotationData.comments || ''
      ];

      const { rows } = await client.query(quotationQuery, quotationValues);
      const quotationId = rows[0].id;

      // Insert quotation items
      if (quotationData.products && quotationData.products.length > 0) {
        const itemQuery = `
          INSERT INTO quotation_items (
            quotation_id, item_code, item_name, quantity,
            unit_price_excluding_tax, tax_per_product, total_price
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `;

        for (const product of quotationData.products) {
          await client.query(itemQuery, [
            quotationId,
            product.item_code || '',
            product.name || product.item_name || '',
            product.quantity || 1,
            product.unitPriceExcludingTax || 0,
            product.taxPerUnit || 0,
            product.totalPrice || 0
          ]);
        }
      }

      await client.query('COMMIT');
      return quotationId;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error in createQuotation:', error);
      throw error;
    } finally {
      client.release();
    }
  },

  getAllQuotations: async () => {
    try {
      const query = `
        SELECT
          q.id AS quotation_id,
          q.reference_number,
          q.date,
          q.billing_name,
          q.billing_address,
          q.billing_email,
          q.billing_phone,
          q.shipping_name,
          q.shipping_address,
          q.shipping_email,
          q.shipping_phone,
          q.payment_method,
          q.subtotal,
          q.tax,
          q.total,
          q.salesperson_name,
          q.company_name,
          q.comments,
          q.created_at,
          qi.item_code,
          qi.item_name,
          qi.quantity,
          qi.unit_price_excluding_tax,
          qi.tax_per_product,
          qi.total_price
        FROM quotations q
        LEFT JOIN quotation_items qi ON q.id = qi.quotation_id
        ORDER BY q.date DESC, q.id DESC
      `;

      const { rows } = await pool.query(query);

      // Group the results by quotation
      const quotationsMap = new Map();

      rows.forEach(row => {
        const quotationId = row.quotation_id;

        if (!quotationsMap.has(quotationId)) {
          quotationsMap.set(quotationId, {
            quotation_id: quotationId,
            reference_number: row.reference_number,
            date: row.date,
            billing_name: row.billing_name,
            billing_address: row.billing_address,
            billing_email: row.billing_email,
            billing_phone: row.billing_phone,
            shipping_name: row.shipping_name,
            shipping_address: row.shipping_address,
            shipping_email: row.shipping_email,
            shipping_phone: row.shipping_phone,
            payment_method: row.payment_method,
            subtotal: row.subtotal,
            tax: row.tax,
            total: row.total,
            salesperson_name: row.salesperson_name,
            company_name: row.company_name || 'Shans Accessories PTY LTD',
            comments: row.comments,
            created_at: row.created_at,
            items: []
          });
        }

        // Add item if it exists
        if (row.item_code) {
          quotationsMap.get(quotationId).items.push({
            item_code: row.item_code,
            item_name: row.item_name,
            quantity: row.quantity,
            unit_price_excluding_tax: row.unit_price_excluding_tax,
            tax_per_product: row.tax_per_product,
            total_price: row.total_price
          });
        }
      });

      return Array.from(quotationsMap.values());
    } catch (error) {
      console.error('Error in getAllQuotations:', error);
      throw error;
    }
  },

  getQuotationById: async (quotationId) => {
    try {
      const query = `
        SELECT
          q.id AS quotation_id,
          q.reference_number,
          q.date,
          q.billing_name,
          q.billing_address,
          q.billing_email,
          q.billing_phone,
          q.shipping_name,
          q.shipping_address,
          q.shipping_email,
          q.shipping_phone,
          q.payment_method,
          q.subtotal,
          q.tax,
          q.total,
          q.salesperson_name,
          q.company_name,
          q.comments,
          q.created_at,
          qi.item_code,
          qi.item_name,
          qi.quantity,
          qi.unit_price_excluding_tax,
          qi.tax_per_product,
          qi.total_price
        FROM quotations q
        LEFT JOIN quotation_items qi ON q.id = qi.quotation_id
        WHERE q.id = $1
      `;

      const { rows } = await pool.query(query, [quotationId]);

      if (rows.length === 0) {
        return null;
      }

      // Construct the quotation object with items
      const quotation = {
        quotation_id: rows[0].quotation_id,
        reference_number: rows[0].reference_number,
        date: rows[0].date,
        billing_name: rows[0].billing_name,
        billing_address: rows[0].billing_address,
        billing_email: rows[0].billing_email,
        billing_phone: rows[0].billing_phone,
        shipping_name: rows[0].shipping_name,
        shipping_address: rows[0].shipping_address,
        shipping_email: rows[0].shipping_email,
        shipping_phone: rows[0].shipping_phone,
        payment_method: rows[0].payment_method,
        subtotal: rows[0].subtotal,
        tax: rows[0].tax,
        total: rows[0].total,
        salesperson_name: rows[0].salesperson_name,
        company_name: rows[0].company_name || 'Shans Accessories PTY LTD',
        comments: rows[0].comments,
        created_at: rows[0].created_at,
        items: []
      };

      // Add items
      rows.forEach(row => {
        if (row.item_code) {
          quotation.items.push({
            item_code: row.item_code,
            item_name: row.item_name,
            quantity: row.quantity,
            unit_price_excluding_tax: row.unit_price_excluding_tax,
            tax_per_product: row.tax_per_product,
            total_price: row.total_price
          });
        }
      });

      return quotation;
    } catch (error) {
      console.error('Error in getQuotationById:', error);
      throw error;
    }
  },

  updateQuotation: async (quotationId, quotationData) => {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Update quotation record
      const quotationQuery = `
        UPDATE quotations SET
          reference_number = $1, date = $2, billing_name = $3, billing_address = $4,
          billing_email = $5, billing_phone = $6, shipping_name = $7, shipping_address = $8,
          shipping_email = $9, shipping_phone = $10, payment_method = $11, subtotal = $12,
          tax = $13, total = $14, salesperson_name = $15, company_name = $16, comments = $17
        WHERE id = $18
        RETURNING id
      `;

      const quotationValues = [
        quotationData.reference_number,
        quotationData.date,
        quotationData.billing_name,
        quotationData.billing_address,
        quotationData.billing_email,
        quotationData.billing_phone,
        quotationData.shipping_name,
        quotationData.shipping_address,
        quotationData.shipping_email,
        quotationData.shipping_phone,
        quotationData.payment_method,
        quotationData.subtotal,
        quotationData.tax,
        quotationData.total,
        quotationData.salesperson_name || '',
        quotationData.company_name || 'Shans Accessories PTY LTD',
        quotationData.comments || '',
        quotationId
      ];

      const { rows } = await client.query(quotationQuery, quotationValues);
      if (rows.length === 0) {
        throw new Error('Quotation not found');
      }

      // Delete existing quotation items
      await client.query('DELETE FROM quotation_items WHERE quotation_id = $1', [quotationId]);

      // Insert updated quotation items
      if (quotationData.products && quotationData.products.length > 0) {
        const itemQuery = `
          INSERT INTO quotation_items (
            quotation_id, item_code, item_name, quantity,
            unit_price_excluding_tax, tax_per_product, total_price
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `;

        for (const product of quotationData.products) {
          await client.query(itemQuery, [
            quotationId,
            product.item_code || '',
            product.name || product.item_name || '',
            product.quantity || 1,
            product.unitPriceExcludingTax || 0,
            product.taxPerUnit || 0,
            product.totalPrice || 0
          ]);
        }
      }

      await client.query('COMMIT');
      return quotationId;
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error in updateQuotation:', error);
      throw error;
    } finally {
      client.release();
    }
  },

  deleteQuotation: async (quotationId) => {
    try {
      const query = 'DELETE FROM quotations WHERE id = $1 RETURNING id';
      const { rows } = await pool.query(query, [quotationId]);
      return rows.length > 0;
    } catch (error) {
      console.error('Error in deleteQuotation:', error);
      throw error;
    }
  }
};

module.exports = quotationModel;
